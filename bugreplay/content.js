/* global chrome */

// content.js
let isCapturing = false;
let currentSessionId = null;
const originalConsole = {};
const originalFetch = window.fetch;
let mutationObserver = null;
let interactionListeners = [];
let lastScrollTime = 0;

/**
 * Send log data to the extension popup
 * @param {Object} logData - The log data to send
 */
function sendLog(logData) {
  try {
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage({
        type: 'LOG_ENTRY_FROM_CONTENT',
        payload: {
          ...logData,
          timestamp: new Date().toISOString(), // Ensure timestamp is fresh
        },
      });
    } else {
      // console.warn('BugReplay: chrome.runtime.sendMessage not available to send log.');
    }
  } catch (error) {
    // console.warn('BugReplay: Could not send log, popup might be closed.', error);
    // This can happen if the popup is not open.
    // Future enhancement: buffer logs in background script or local storage.
  }
}

/**
 * Override console methods to capture logs
 */
function overrideConsole() {
  ['log', 'warn', 'error', 'info', 'debug'].forEach((method) => {
    if (console[method]) {
      originalConsole[method] = console[method];
      console[method] = (...args) => {
        originalConsole[method](...args);
        if (isCapturing) {
          const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
          sendLog({
            type: method === 'error' ? 'CONSOLE_ERROR' : 'CONSOLE_LOG',
            message: `[Console.${method}] ${message}`,
            status: method === 'warn' ? 'warning' : undefined,
          });
        }
      };
    }
  });
}

/**
 * Restore original console methods
 */
function restoreConsole() {
  Object.keys(originalConsole).forEach((method) => {
    if (originalConsole[method]) {
      console[method] = originalConsole[method];
    }
  });
}

/**
 * Override fetch to capture network requests
 */
function overrideFetch() {
    window.fetch = async (...args) => {
        const [resource, config] = args;
        const requestUrl = resource instanceof Request ? resource.url : String(resource);
        const requestMethod = config?.method?.toUpperCase() || (resource instanceof Request ? resource.method.toUpperCase() : 'GET');

        if (isCapturing) {
            sendLog({
                type: 'NETWORK_REQUEST',
                message: `${requestMethod} ${requestUrl} - Initiated`,
                status: 'pending'
            });
        }

        try {
            const response = await originalFetch(...args);
            if (isCapturing) {
                sendLog({
                    type: 'NETWORK_REQUEST',
                    message: `${requestMethod} ${requestUrl} - ${response.status} ${response.statusText}`,
                    status: response.ok ? 'success' : 'error',
                });
            }
            return response;
        } catch (error) {
            if (isCapturing) {
                sendLog({
                    type: 'NETWORK_REQUEST',
                    message: `${requestMethod} ${requestUrl} - Failed: ${error.message}`,
                    status: 'error',
                });
            }
            throw error;
        }
    };
}

/**
 * Restore original fetch function
 */
function restoreFetch() {
    window.fetch = originalFetch;
}

/**
 * Handle user click actions
 * @param {Event} event - Click event
 */
function handleUserAction(event) {
  if (!isCapturing || !event.target) return;

  const target = event.target;
  const tagName = target.tagName.toLowerCase();
  const className = target.className || '';
  const id = target.id || '';
  const text = target.textContent ? target.textContent.substring(0, 50) : '';

  let description = `User clicked on ${tagName}`;
  if (id) description += ` with id="${id}"`;
  if (className) description += ` with class="${className}"`;
  if (text) description += ` containing text "${text}"`;

  const interaction = {
    type: 'click',
    target: generateSelector(target),
    coordinates: { x: event.clientX, y: event.clientY },
    timestamp: new Date(),
    description: description
  };

  sendLog({
    type: 'USER_ACTION',
    message: description,
    interaction: interaction
  });
}

/**
 * Handle scroll events
 * @param {Event} event - Scroll event
 */
function handleScrollAction(event) {
  if (!isCapturing) return;

  const now = Date.now();
  if (now - lastScrollTime < 100) return; // Throttle scroll events
  lastScrollTime = now;

  const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
  const scrollY = window.pageYOffset || document.documentElement.scrollTop;

  const interaction = {
    type: 'scroll',
    target: 'window',
    coordinates: { x: scrollX, y: scrollY },
    timestamp: new Date(),
    description: `Scrolled to position (${scrollX}, ${scrollY})`
  };

  sendLog({
    type: 'USER_ACTION',
    message: `User scrolled to (${scrollX}, ${scrollY})`,
    interaction: interaction
  });
}

/**
 * Handle keyboard input
 * @param {Event} event - Keyboard event
 */
function handleKeyboardAction(event) {
  if (!isCapturing) return;

  const target = event.target;
  const tagName = target.tagName.toLowerCase();
  const type = target.type || '';

  if (tagName === 'input' || tagName === 'textarea') {
    const interaction = {
      type: 'keypress',
      target: generateSelector(target),
      key: event.key,
      value: target.value,
      timestamp: new Date(),
      description: `Typed in ${tagName}${type ? `[${type}]` : ''}`
    };

    sendLog({
      type: 'USER_ACTION',
      message: `User typed "${event.key}" in ${tagName}${type ? `[${type}]` : ''}`,
      interaction: interaction
    });
  }
}

/**
 * Handle form submissions
 * @param {Event} event - Submit event
 */
function handleFormSubmission(event) {
  if (!isCapturing) return;

  const form = event.target;
  const action = form.action || window.location.href;
  const method = form.method || 'GET';

  const interaction = {
    type: 'submit',
    target: generateSelector(form),
    action: action,
    method: method,
    timestamp: new Date(),
    description: `Submitted form to ${action}`
  };

  sendLog({
    type: 'USER_ACTION',
    message: `User submitted form to ${action} (${method})`,
    interaction: interaction
  });
}

/**
 * Generate CSS selector for an element
 * @param {Element} element - DOM element
 * @returns {string} CSS selector
 */
function generateSelector(element) {
  if (element.id) {
    return `#${element.id}`;
  }

  if (element.className) {
    const classes = element.className.split(' ').filter(c => c.trim());
    if (classes.length > 0) {
      return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
    }
  }

  // Generate path-based selector
  const path = [];
  let current = element;

  while (current && current.nodeType === Node.ELEMENT_NODE) {
    let selector = current.tagName.toLowerCase();

    if (current.id) {
      selector += `#${current.id}`;
      path.unshift(selector);
      break;
    }

    const siblings = Array.from(current.parentNode?.children || []);
    const index = siblings.indexOf(current);
    if (index > 0) {
      selector += `:nth-child(${index + 1})`;
    }

    path.unshift(selector);
    current = current.parentNode;

    if (path.length > 5) break; // Limit depth
  }

  return path.join(' > ');
}

/**
 * Initialize DOM mutation observer
 */
function initializeMutationObserver() {
  mutationObserver = new MutationObserver((mutations) => {
    if (!isCapturing) return;

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        const addedElements = Array.from(mutation.addedNodes)
          .filter(node => node.nodeType === Node.ELEMENT_NODE)
          .map(node => node.tagName?.toLowerCase())
          .filter(Boolean);

        if (addedElements.length > 0) {
          sendLog({
            type: 'DOM_MUTATION',
            message: `DOM elements added: ${addedElements.join(', ')}`,
            timestamp: new Date()
          });
        }
      }

      if (mutation.type === 'attributes') {
        sendLog({
          type: 'DOM_MUTATION',
          message: `Attribute '${mutation.attributeName}' changed on ${mutation.target.tagName?.toLowerCase()}`,
          timestamp: new Date()
        });
      }
    });
  });

  mutationObserver.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class', 'style', 'data-*']
  });
}

/**
 * Setup all interaction listeners
 */
function setupInteractionListeners() {
  const listeners = [
    { event: 'click', handler: handleUserAction, options: true },
    { event: 'scroll', handler: handleScrollAction, options: false },
    { event: 'keydown', handler: handleKeyboardAction, options: false },
    { event: 'submit', handler: handleFormSubmission, options: false }
  ];

  listeners.forEach(({ event, handler, options }) => {
    document.addEventListener(event, handler, options);
    interactionListeners.push({ event, handler, options });
  });
}

/**
 * Remove all interaction listeners
 */
function removeInteractionListeners() {
  interactionListeners.forEach(({ event, handler, options }) => {
    document.removeEventListener(event, handler, options);
  });
  interactionListeners = [];
}

/**
 * Start capturing user actions and logs
 */
function startCapturing() {
  if (isCapturing) return;
  isCapturing = true;
  currentSessionId = arguments[0]?.sessionId || null;

  overrideConsole();
  overrideFetch();
  setupInteractionListeners();
  initializeMutationObserver();

  sendLog({ type: 'SYSTEM', message: 'Content script started comprehensive capturing.' });
}

/**
 * Stop capturing user actions and logs
 */
function stopCapturing() {
  if (!isCapturing) return;
  isCapturing = false;
  currentSessionId = null;

  restoreConsole();
  restoreFetch();
  removeInteractionListeners();

  if (mutationObserver) {
    mutationObserver.disconnect();
    mutationObserver = null;
  }

  sendLog({ type: 'SYSTEM', message: 'Content script stopped comprehensive capturing.' });
}

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'START_RECORDING_CONTENT') {
      startCapturing({ sessionId: request.sessionId });
      sendResponse({
        success: true,
        message: 'Enhanced recording started in content script.',
        sessionId: request.sessionId
      });
    } else if (request.type === 'STOP_RECORDING_CONTENT') {
      stopCapturing();
      sendResponse({
        success: true,
        message: 'Enhanced recording stopped in content script.',
        sessionId: request.sessionId
      });
    }
    return true; // Keep message channel open for async response if needed
  });
}

// Initial overrides if the extension starts in a "recording" state (not typical for manual start)
// or handle state from storage if implementing persistence.
// For now, relies on popup message to start.
console.log('BugReplay content script loaded.');