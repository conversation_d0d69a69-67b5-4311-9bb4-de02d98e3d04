# BugReplay Chrome Extension

A comprehensive browser extension that records user interactions, screen activity, network requests, console logs, and DOM changes to create detailed bug reports with complete reproduction data.

## 🚀 Features

### Real Recording Functionality
- **Comprehensive User Interaction Tracking**: Captures clicks, scrolls, keyboard inputs, form submissions with precise coordinates and timestamps
- **Screen Recording**: Records video of the active tab during the session using Chrome's desktopCapture API
- **Periodic Screenshots**: Takes screenshots every 5 seconds during recording for visual timeline
- **Console Log Monitoring**: Captures all console messages (log, warn, error, info) with stack traces
- **Network Request Tracking**: Monitors and logs all network requests/responses with full HAR (HTTP Archive) export
- **DOM Mutation Detection**: Tracks dynamic changes to page structure and attributes
- **Session Management**: Organized recording sessions with unique IDs and metadata
- **🔄 Persistent State Management**: Recording state and data persist across popup close/reopen cycles

### Enhanced Bug Reporting
- **Video Playback**: Review recorded sessions with embedded video player
- **Screenshot Timeline**: <PERSON>rowse captured screenshots with click-to-expand functionality
- **HAR File Export**: Download complete network data for detailed analysis
- **Comprehensive Logs**: All captured data organized by timestamp and type
- **Attachment Management**: Automatic attachment of videos, screenshots, and network data

### Developer Tools Integration
- **Chrome Extension APIs**: Utilizes tabs, debugger, webRequest, desktopCapture, and storage APIs
- **Real-time Data Processing**: Efficient handling of large recording sessions
- **Cross-tab Support**: Records activity on any active browser tab
- **Error Simulation**: Legacy support for testing error reporting workflows

## 📦 Installation

1. Clone this repository:
   ```bash
   git clone <repository-url>
   cd bugreplay
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the extension:
   ```bash
   npm run build
   ```

4. Load in Chrome:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select the `dist` folder

## 🎯 Usage

### Starting a Recording Session
1. Click the BugReplay icon in your browser toolbar
2. Click "Start Recording" to begin comprehensive data capture
3. The extension will:
   - Start screen recording of the active tab
   - Begin monitoring user interactions
   - Track network requests and responses
   - Capture console logs and DOM changes
   - Take periodic screenshots

### During Recording
- A red recording indicator shows active capture
- All user interactions are automatically tracked
- Network requests are monitored in real-time
- Console logs are captured with full context
- **Recording continues even when popup is closed**

### Stopping and Reviewing
1. **Reopen the popup** if it was closed during recording
2. Click "Stop Recording" when you've reproduced the issue
3. The extension processes all captured data
4. A comprehensive bug report modal opens with:
   - Video recording of the session
   - Screenshot timeline
   - Complete interaction log (including actions performed while popup was closed)
   - Network request data (HAR format)
   - Console logs and errors

### Exporting Data
- **Video**: Embedded player with download option
- **Screenshots**: Click any screenshot to view full-size
- **HAR File**: Download button for network analysis tools
- **Bug Report**: Complete formatted report for issue tracking

## 🛠 Development

### Scripts
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run build:dev` - Build with development settings
- `npm run watch` - Build and watch for changes
- `npm run clean` - Clean build artifacts

### Architecture

#### Background Script (`background.js`)
- **RecordingManager**: Centralized session management
- **Network Monitoring**: WebRequest API integration for HAR generation
- **Screenshot Capture**: Periodic visual snapshots
- **Session Storage**: Chrome storage API for data persistence

#### Content Script (`content.js`)
- **Interaction Tracking**: Mouse, keyboard, scroll, and form events
- **DOM Mutation Observer**: Real-time page change detection
- **Console Override**: Capture all console output
- **Network Override**: Monitor fetch/XHR requests
- **Selector Generation**: Smart CSS selector creation for elements

#### Popup Interface (`App.jsx`)
- **Recording Controls**: Start/stop recording with real-time status
- **Session Management**: Display current recording state
- **Bug Report Generation**: Process recording data into structured reports
- **Error Simulation**: Legacy testing functionality

#### Enhanced Bug Report Modal (`BugReportModal.jsx`)
- **Video Player**: Embedded session playback
- **Screenshot Gallery**: Interactive timeline view
- **HAR Export**: Network data download functionality
- **Attachment Management**: Comprehensive file handling

## 🔧 Technical Implementation

### Permissions Required
- `storage` - Session data persistence
- `activeTab` - Current tab access
- `scripting` - Content script injection
- `tabs` - Tab management and screenshots
- `debugger` - Advanced debugging capabilities
- `webRequest` - Network monitoring
- `desktopCapture` - Screen recording
- `<all_urls>` - Universal site access

### Data Structures

#### Recording Session
```javascript
{
  id: "session_timestamp_random",
  startTime: Date,
  endTime: Date,
  tabId: number,
  url: string,
  title: string,
  logs: LogEntry[],
  screenshots: string[], // Data URLs
  videoUrl: string, // Blob URL
  harData: HARObject,
  state: RecordingState
}
```

#### User Interaction
```javascript
{
  type: "click|scroll|keypress|submit",
  target: string, // CSS selector
  coordinates: { x: number, y: number },
  value: string, // For form inputs
  timestamp: Date,
  description: string
}
```

### Performance Considerations
- **Throttled Events**: Scroll events limited to 100ms intervals
- **Efficient Selectors**: Smart CSS selector generation with depth limits
- **Memory Management**: Automatic cleanup of recording resources
- **Chunked Video**: 1-second video chunks for better performance
- **Compressed Storage**: Optimized data structures for large sessions

## 🧪 Testing Persistence

### Test the Persistence Fix
1. Open `test-persistence.html` in your browser
2. Follow the step-by-step instructions on the page
3. Verify that recording state persists across popup close/reopen cycles
4. Confirm that all captured data is included in the final bug report

### Expected Behavior
- ✅ Recording continues when popup is closed
- ✅ State is restored when popup reopens
- ✅ All logs and data are preserved
- ✅ Complete session data in bug reports

## 🐛 Troubleshooting

### Common Issues
1. **Recording not starting**: Check extension permissions and active tab
2. **Video not capturing**: Ensure desktopCapture permission is granted
3. **Network data missing**: Verify webRequest permission for target sites
4. **Large file sizes**: Consider shorter recording sessions for better performance
5. **State not persisting**: Check Chrome storage permissions and available storage space
6. **Content script errors**: Extension automatically injects content scripts - no action needed
7. **Connection errors**: If you see connection errors, try refreshing the page and starting recording again

### Debug Mode
Enable Chrome DevTools for the extension:
1. Right-click extension icon → "Inspect popup"
2. Check console for detailed logging
3. Monitor network tab for API calls
4. Review storage tab for session data
5. Check `chrome://extensions/` for any permission issues

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
