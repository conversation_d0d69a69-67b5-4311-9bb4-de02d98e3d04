
/* global chrome */

// background.js

// Import RecordingManager - Note: Chrome extensions don't support ES6 imports in service workers
// We'll need to implement the RecordingManager inline or use a different approach

/**
 * RecordingManager - Centralized recording management
 * Handles coordination between different recording components
 */
class RecordingManager {
  constructor() {
    this.currentSession = null;
    this.state = 'IDLE'; // RecordingState.IDLE
    this.mediaRecorder = null;
    this.recordedChunks = [];
    this.screenshotInterval = null;
    this.networkEntries = [];
    this.harData = null;

    // Bind methods
    this.handleNetworkRequest = this.handleNetworkRequest.bind(this);
    this.handleNetworkResponse = this.handleNetworkResponse.bind(this);
    this.captureScreenshot = this.captureScreenshot.bind(this);
  }

  /**
   * Start a new recording session
   * @param {number} tabId - ID of the tab to record
   * @returns {Promise<string>} Session ID
   */
  async startRecording(tabId) {
    if (this.state !== 'IDLE') {
      throw new Error('Recording already in progress');
    }

    this.state = 'STARTING';

    try {
      // Get tab information
      const tab = await this.getTabInfo(tabId);

      // Create new session
      this.currentSession = {
        id: this.generateSessionId(),
        startTime: new Date(),
        endTime: null,
        tabId: tabId,
        url: tab.url,
        title: tab.title,
        logs: [],
        screenshots: [],
        videoUrl: null,
        harData: null,
        state: 'STARTING'
      };

      // Initialize recording components
      await this.initializeNetworkMonitoring();
      await this.startScreenshotCapture();

      this.state = 'RECORDING';
      this.currentSession.state = 'RECORDING';

      // Store session in chrome storage
      await this.saveSession();

      console.log('Recording started for session:', this.currentSession.id);
      return this.currentSession.id;

    } catch (error) {
      this.state = 'ERROR';
      console.error('Failed to start recording:', error);
      throw error;
    }
  }

  /**
   * Stop the current recording session
   * @returns {Promise<RecordingSession>} Completed session data
   */
  async stopRecording() {
    if (this.state !== 'RECORDING') {
      throw new Error('No recording in progress');
    }

    this.state = 'STOPPING';

    try {
      // Stop all recording components
      await this.stopNetworkMonitoring();
      this.stopScreenshotCapture();

      // Finalize session
      this.currentSession.endTime = new Date();
      this.currentSession.state = 'PROCESSING';

      // Process and compile data
      await this.processRecordingData();

      this.currentSession.state = 'IDLE';
      await this.saveSession();

      const completedSession = { ...this.currentSession };
      this.currentSession = null;
      this.state = 'IDLE';

      console.log('Recording stopped for session:', completedSession.id);
      return completedSession;

    } catch (error) {
      this.state = 'ERROR';
      console.error('Failed to stop recording:', error);
      throw error;
    }
  }

  /**
   * Initialize network monitoring using webRequest API
   */
  async initializeNetworkMonitoring() {
    this.networkEntries = [];

    // Monitor request start
    chrome.webRequest.onBeforeRequest.addListener(
      this.handleNetworkRequest,
      { urls: ['<all_urls>'] },
      ['requestBody']
    );

    // Monitor response
    chrome.webRequest.onCompleted.addListener(
      this.handleNetworkResponse,
      { urls: ['<all_urls>'] },
      ['responseHeaders']
    );
  }

  /**
   * Stop network monitoring
   */
  async stopNetworkMonitoring() {
    chrome.webRequest.onBeforeRequest.removeListener(this.handleNetworkRequest);
    chrome.webRequest.onCompleted.removeListener(this.handleNetworkResponse);

    // Generate HAR data
    this.generateHarData();
  }

  /**
   * Handle network request
   * @param {Object} details - Request details
   */
  handleNetworkRequest(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    const entry = {
      requestId: details.requestId,
      url: details.url,
      method: details.method,
      startTime: details.timeStamp,
      requestBody: details.requestBody,
      tabId: details.tabId
    };

    this.networkEntries.push(entry);

    // Add to session logs
    this.addLogEntry({
      type: 'NETWORK_REQUEST',
      message: `${details.method} ${details.url}`,
      timestamp: new Date(details.timeStamp)
    });
  }

  /**
   * Handle network response
   * @param {Object} details - Response details
   */
  handleNetworkResponse(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    const entry = this.networkEntries.find(e => e.requestId === details.requestId);
    if (entry) {
      entry.status = details.statusCode;
      entry.responseHeaders = details.responseHeaders;
      entry.endTime = details.timeStamp;
      entry.size = details.responseSize || 0;
    }

    // Add to session logs
    this.addLogEntry({
      type: 'NETWORK_RESPONSE',
      message: `${details.statusCode} ${details.url}`,
      status: details.statusCode >= 400 ? 'error' : 'success',
      timestamp: new Date(details.timeStamp)
    });
  }

  /**
   * Start periodic screenshot capture
   */
  async startScreenshotCapture() {
    this.screenshotInterval = setInterval(async () => {
      try {
        await this.captureScreenshot();
      } catch (error) {
        console.warn('Failed to capture screenshot:', error);
      }
    }, 5000); // Capture every 5 seconds
  }

  /**
   * Stop screenshot capture
   */
  stopScreenshotCapture() {
    if (this.screenshotInterval) {
      clearInterval(this.screenshotInterval);
      this.screenshotInterval = null;
    }
  }

  /**
   * Capture a screenshot of the active tab
   */
  async captureScreenshot() {
    return new Promise((resolve, reject) => {
      chrome.tabs.captureVisibleTab(null, { format: 'png' }, (dataUrl) => {
        if (chrome.runtime.lastError || !dataUrl) {
          reject(new Error(chrome.runtime.lastError?.message || 'Screenshot failed'));
          return;
        }

        this.currentSession.screenshots.push(dataUrl);
        this.addLogEntry({
          type: 'SCREENSHOT',
          message: 'Screenshot captured',
          timestamp: new Date()
        });

        resolve(dataUrl);
      });
    });
  }

  /**
   * Add a log entry to the current session
   * @param {Object} logEntry - Log entry to add
   */
  addLogEntry(logEntry) {
    if (!this.currentSession) return;

    const entry = {
      id: Date.now() + Math.random(),
      timestamp: logEntry.timestamp || new Date(),
      ...logEntry
    };

    this.currentSession.logs.push(entry);
  }

  /**
   * Generate HAR (HTTP Archive) data from network entries
   */
  generateHarData() {
    this.currentSession.harData = {
      log: {
        version: '1.2',
        creator: {
          name: 'BugReplay',
          version: '1.0.0'
        },
        entries: this.networkEntries.map(entry => ({
          startedDateTime: new Date(entry.startTime).toISOString(),
          time: entry.endTime ? entry.endTime - entry.startTime : 0,
          request: {
            method: entry.method,
            url: entry.url,
            httpVersion: 'HTTP/1.1',
            headers: [],
            queryString: [],
            postData: entry.requestBody ? {
              mimeType: 'application/json',
              text: JSON.stringify(entry.requestBody)
            } : undefined,
            headersSize: -1,
            bodySize: -1
          },
          response: {
            status: entry.status || 0,
            statusText: '',
            httpVersion: 'HTTP/1.1',
            headers: entry.responseHeaders || [],
            content: {
              size: entry.size || 0,
              mimeType: 'application/octet-stream'
            },
            redirectURL: '',
            headersSize: -1,
            bodySize: entry.size || 0
          },
          cache: {},
          timings: {
            send: 0,
            wait: entry.endTime ? entry.endTime - entry.startTime : 0,
            receive: 0
          }
        }))
      }
    };
  }

  /**
   * Process and finalize recording data
   */
  async processRecordingData() {
    // Additional processing can be added here
    console.log('Processing recording data for session:', this.currentSession.id);
  }

  /**
   * Save session to chrome storage
   */
  async saveSession() {
    if (!this.currentSession) return;

    const sessionData = { ...this.currentSession };
    await chrome.storage.local.set({
      [`session_${sessionData.id}`]: sessionData,
      'currentSessionId': sessionData.id
    });
  }

  /**
   * Get tab information
   * @param {number} tabId - Tab ID
   * @returns {Promise<Object>} Tab information
   */
  async getTabInfo(tabId) {
    return new Promise((resolve, reject) => {
      chrome.tabs.get(tabId, (tab) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        resolve(tab);
      });
    });
  }

  /**
   * Generate unique session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current recording state
   * @returns {string} Current state
   */
  getState() {
    return this.state;
  }

  /**
   * Get current session
   * @returns {Object|null} Current session or null
   */
  getCurrentSession() {
    return this.currentSession;
  }
}

// Initialize recording manager
const recordingManager = new RecordingManager();

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onInstalled) {
  chrome.runtime.onInstalled.addListener(() => {
    console.log('BugReplay extension installed/updated.');
  });
}

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    // Handle recording management requests
    if (request.type === 'START_RECORDING_BACKGROUND') {
      handleStartRecording(request, sender, sendResponse);
      return true;
    } else if (request.type === 'STOP_RECORDING_BACKGROUND') {
      handleStopRecording(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_RECORDING_STATE') {
      sendResponse({
        state: recordingManager.getState(),
        session: recordingManager.getCurrentSession()
      });
      return false;
    } else if (request.type === 'ADD_LOG_ENTRY') {
      recordingManager.addLogEntry(request.logEntry);
      sendResponse({ success: true });
      return false;
    } else if (request.type === 'GET_ACTIVE_TAB_INFO') {
      if (sender.tab && (sender.tab.url || sender.tab.title)) { // Message from content script, which has tab info
        sendResponse({ title: sender.tab.title, url: sender.tab.url });
      } else { // Message likely from popup, query for active tab
        if (chrome.tabs && chrome.tabs.query) {
          chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (chrome.runtime.lastError) {
              console.error('Background: Error querying active tab for GET_ACTIVE_TAB_INFO:', chrome.runtime.lastError.message);
              sendResponse({ error: `Background: Error querying active tab: ${chrome.runtime.lastError.message}` });
              return;
            }
            if (tabs && tabs[0]) {
              sendResponse({ title: tabs[0].title || tabs[0].url, url: tabs[0].url });
            } else {
              sendResponse({ error: "Background: No active tab found for GET_ACTIVE_TAB_INFO." });
            }
          });
        } else {
          sendResponse({ error: "Background: chrome.tabs.query not available for GET_ACTIVE_TAB_INFO." });
        }
      }
      return true; // Indicates that the response is sent asynchronously
    } else if (request.target === 'content_script_via_background') {
      if (chrome.tabs && chrome.tabs.query && chrome.tabs.sendMessage) {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (chrome.runtime.lastError) {
            console.error('Background: Error querying active tab for relay:', chrome.runtime.lastError.message);
            sendResponse({ error: `Background: Error querying active tab for relay: ${chrome.runtime.lastError.message}` });
            return;
          }
          if (tabs && tabs[0] && tabs[0].id) {
            const messageForContentScript = { ...request };
            delete messageForContentScript.target; // Remove the routing target property
            
            chrome.tabs.sendMessage(tabs[0].id, messageForContentScript, (responseFromContent) => {
              if (chrome.runtime.lastError) {
                // Content script might not be there or might not respond.
                // This isn't always a critical error for the background script itself,
                // but the popup needs to know.
                console.warn('Background: Error sending to content script or no response:', chrome.runtime.lastError.message);
                sendResponse({ error: `Background: Error sending to content script: ${chrome.runtime.lastError.message}`, details: responseFromContent });
                return;
              }
              sendResponse(responseFromContent);
            });
          } else {
            sendResponse({ error: "Background: No active tab found to relay message." });
          }
        });
      } else {
        sendResponse({ error: "Background: chrome.tabs API not available for relay." });
      }
      return true; // Asynchronous response
    }
    // Add more message handlers if needed, ensure 'return true' for async.
  });
}

/**
 * Handle start recording request
 * @param {Object} request - Message request
 * @param {Object} sender - Message sender
 * @param {Function} sendResponse - Response callback
 */
async function handleStartRecording(request, sender, sendResponse) {
  try {
    // Get active tab
    const tabs = await new Promise((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        resolve(tabs);
      });
    });

    if (!tabs || !tabs[0]) {
      throw new Error('No active tab found');
    }

    const tabId = tabs[0].id;
    const sessionId = await recordingManager.startRecording(tabId);

    // Notify content script to start recording
    chrome.tabs.sendMessage(tabId, {
      type: 'START_RECORDING_CONTENT',
      sessionId: sessionId
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.warn('Content script not ready:', chrome.runtime.lastError.message);
      }
    });

    sendResponse({
      success: true,
      sessionId: sessionId,
      message: 'Recording started successfully'
    });
  } catch (error) {
    console.error('Failed to start recording:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle stop recording request
 * @param {Object} request - Message request
 * @param {Object} sender - Message sender
 * @param {Function} sendResponse - Response callback
 */
async function handleStopRecording(request, sender, sendResponse) {
  try {
    const session = await recordingManager.stopRecording();

    // Notify content script to stop recording
    if (session.tabId) {
      chrome.tabs.sendMessage(session.tabId, {
        type: 'STOP_RECORDING_CONTENT',
        sessionId: session.id
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.warn('Content script not available:', chrome.runtime.lastError.message);
        }
      });
    }

    sendResponse({
      success: true,
      session: session,
      message: 'Recording stopped successfully'
    });
  } catch (error) {
    console.error('Failed to stop recording:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}
